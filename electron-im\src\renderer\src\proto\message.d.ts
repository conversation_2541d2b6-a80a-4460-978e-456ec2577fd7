import * as $protobuf from "protobufjs";
import Long = require("long");
/** Namespace simpleim. */
export namespace simpleim {

    /** MessageType enum. */
    enum MessageType {
        HEARTBEAT = 0,
        TEXT_MESSAGE = 1,
        ERROR = 100
    }

    /** Properties of a Heartbeat. */
    interface IHeartbeat {

        /** Heartbeat timestamp */
        timestamp?: (number|Long|null);

        /** Heartbeat status */
        status?: (string|null);
    }

    /** Represents a Heartbeat. */
    class Heartbeat implements IHeartbeat {

        /**
         * Constructs a new Heartbeat.
         * @param [properties] Properties to set
         */
        constructor(properties?: simpleim.IHeartbeat);

        /** Heartbeat timestamp. */
        public timestamp: (number|Long);

        /** Heartbeat status. */
        public status: string;

        /**
         * Creates a new Heartbeat instance using the specified properties.
         * @param [properties] Properties to set
         * @returns Heartbeat instance
         */
        public static create(properties?: simpleim.IHeartbeat): simpleim.Heartbeat;

        /**
         * Encodes the specified Heartbeat message. Does not implicitly {@link simpleim.Heartbeat.verify|verify} messages.
         * @param message Heartbeat message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: simpleim.<PERSON>Hear<PERSON><PERSON>, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified Heartbeat message, length delimited. Does not implicitly {@link simpleim.Heartbeat.verify|verify} messages.
         * @param message Heartbeat message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: simpleim.IHeartbeat, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a Heartbeat message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns Heartbeat
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): simpleim.Heartbeat;

        /**
         * Decodes a Heartbeat message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns Heartbeat
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): simpleim.Heartbeat;

        /**
         * Verifies a Heartbeat message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a Heartbeat message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns Heartbeat
         */
        public static fromObject(object: { [k: string]: any }): simpleim.Heartbeat;

        /**
         * Creates a plain object from a Heartbeat message. Also converts values to other types if specified.
         * @param message Heartbeat
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: simpleim.Heartbeat, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this Heartbeat to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for Heartbeat
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of an ErrorMessage. */
    interface IErrorMessage {

        /** ErrorMessage code */
        code?: (string|null);

        /** ErrorMessage message */
        message?: (string|null);
    }

    /** Represents an ErrorMessage. */
    class ErrorMessage implements IErrorMessage {

        /**
         * Constructs a new ErrorMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: simpleim.IErrorMessage);

        /** ErrorMessage code. */
        public code: string;

        /** ErrorMessage message. */
        public message: string;

        /**
         * Creates a new ErrorMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns ErrorMessage instance
         */
        public static create(properties?: simpleim.IErrorMessage): simpleim.ErrorMessage;

        /**
         * Encodes the specified ErrorMessage message. Does not implicitly {@link simpleim.ErrorMessage.verify|verify} messages.
         * @param message ErrorMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: simpleim.IErrorMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified ErrorMessage message, length delimited. Does not implicitly {@link simpleim.ErrorMessage.verify|verify} messages.
         * @param message ErrorMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: simpleim.IErrorMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes an ErrorMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns ErrorMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): simpleim.ErrorMessage;

        /**
         * Decodes an ErrorMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns ErrorMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): simpleim.ErrorMessage;

        /**
         * Verifies an ErrorMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates an ErrorMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns ErrorMessage
         */
        public static fromObject(object: { [k: string]: any }): simpleim.ErrorMessage;

        /**
         * Creates a plain object from an ErrorMessage message. Also converts values to other types if specified.
         * @param message ErrorMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: simpleim.ErrorMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this ErrorMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for ErrorMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a IMMessage. */
    interface IIMMessage {

        /** IMMessage type */
        type?: (number|null);

        /** IMMessage messageId */
        messageId?: (string|null);

        /** IMMessage timestamp */
        timestamp?: (number|Long|null);

        /** IMMessage token */
        token?: (string|null);

        /** IMMessage textMessage */
        textMessage?: (simpleim.ITextMessage|null);
    }

    /** Represents a IMMessage. */
    class IMMessage implements IIMMessage {

        /**
         * Constructs a new IMMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: simpleim.IIMMessage);

        /** IMMessage type. */
        public type: number;

        /** IMMessage messageId. */
        public messageId: string;

        /** IMMessage timestamp. */
        public timestamp: (number|Long);

        /** IMMessage token. */
        public token: string;

        /** IMMessage textMessage. */
        public textMessage?: (simpleim.ITextMessage|null);

        /**
         * Creates a new IMMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns IMMessage instance
         */
        public static create(properties?: simpleim.IIMMessage): simpleim.IMMessage;

        /**
         * Encodes the specified IMMessage message. Does not implicitly {@link simpleim.IMMessage.verify|verify} messages.
         * @param message IMMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: simpleim.IIMMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified IMMessage message, length delimited. Does not implicitly {@link simpleim.IMMessage.verify|verify} messages.
         * @param message IMMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: simpleim.IIMMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a IMMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns IMMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): simpleim.IMMessage;

        /**
         * Decodes a IMMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns IMMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): simpleim.IMMessage;

        /**
         * Verifies a IMMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a IMMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns IMMessage
         */
        public static fromObject(object: { [k: string]: any }): simpleim.IMMessage;

        /**
         * Creates a plain object from a IMMessage message. Also converts values to other types if specified.
         * @param message IMMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: simpleim.IMMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this IMMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for IMMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }

    /** Properties of a TextMessage. */
    interface ITextMessage {

        /** TextMessage receiverId */
        receiverId?: (string|null);

        /** TextMessage content */
        content?: (string|null);
    }

    /** Represents a TextMessage. */
    class TextMessage implements ITextMessage {

        /**
         * Constructs a new TextMessage.
         * @param [properties] Properties to set
         */
        constructor(properties?: simpleim.ITextMessage);

        /** TextMessage receiverId. */
        public receiverId: string;

        /** TextMessage content. */
        public content: string;

        /**
         * Creates a new TextMessage instance using the specified properties.
         * @param [properties] Properties to set
         * @returns TextMessage instance
         */
        public static create(properties?: simpleim.ITextMessage): simpleim.TextMessage;

        /**
         * Encodes the specified TextMessage message. Does not implicitly {@link simpleim.TextMessage.verify|verify} messages.
         * @param message TextMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encode(message: simpleim.ITextMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Encodes the specified TextMessage message, length delimited. Does not implicitly {@link simpleim.TextMessage.verify|verify} messages.
         * @param message TextMessage message or plain object to encode
         * @param [writer] Writer to encode to
         * @returns Writer
         */
        public static encodeDelimited(message: simpleim.ITextMessage, writer?: $protobuf.Writer): $protobuf.Writer;

        /**
         * Decodes a TextMessage message from the specified reader or buffer.
         * @param reader Reader or buffer to decode from
         * @param [length] Message length if known beforehand
         * @returns TextMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decode(reader: ($protobuf.Reader|Uint8Array), length?: number): simpleim.TextMessage;

        /**
         * Decodes a TextMessage message from the specified reader or buffer, length delimited.
         * @param reader Reader or buffer to decode from
         * @returns TextMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        public static decodeDelimited(reader: ($protobuf.Reader|Uint8Array)): simpleim.TextMessage;

        /**
         * Verifies a TextMessage message.
         * @param message Plain object to verify
         * @returns `null` if valid, otherwise the reason why it is not
         */
        public static verify(message: { [k: string]: any }): (string|null);

        /**
         * Creates a TextMessage message from a plain object. Also converts values to their respective internal types.
         * @param object Plain object
         * @returns TextMessage
         */
        public static fromObject(object: { [k: string]: any }): simpleim.TextMessage;

        /**
         * Creates a plain object from a TextMessage message. Also converts values to other types if specified.
         * @param message TextMessage
         * @param [options] Conversion options
         * @returns Plain object
         */
        public static toObject(message: simpleim.TextMessage, options?: $protobuf.IConversionOptions): { [k: string]: any };

        /**
         * Converts this TextMessage to JSON.
         * @returns JSON object
         */
        public toJSON(): { [k: string]: any };

        /**
         * Gets the default type url for TextMessage
         * @param [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns The default type url
         */
        public static getTypeUrl(typeUrlPrefix?: string): string;
    }
}

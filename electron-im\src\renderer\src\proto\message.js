/*eslint-disable block-scoped-var, id-length, no-control-regex, no-magic-numbers, no-prototype-builtins, no-redeclare, no-shadow, no-var, sort-vars*/
import * as $protobuf from "protobufjs/minimal";

// Common aliases
const $Reader = $protobuf.Reader, $Writer = $protobuf.Writer, $util = $protobuf.util;

// Exported root namespace
const $root = $protobuf.roots["default"] || ($protobuf.roots["default"] = {});

export const simpleim = $root.simpleim = (() => {

    /**
     * Namespace simpleim.
     * @exports simpleim
     * @namespace
     */
    const simpleim = {};

    /**
     * MessageType enum.
     * @name simpleim.MessageType
     * @enum {number}
     * @property {number} HEARTBEAT=0 HEARTBEAT value
     * @property {number} TEXT_MESSAGE=1 TEXT_MESSAGE value
     * @property {number} ERROR=100 ERROR value
     */
    simpleim.MessageType = (function() {
        const valuesById = {}, values = Object.create(valuesById);
        values[valuesById[0] = "HEARTBEAT"] = 0;
        values[valuesById[1] = "TEXT_MESSAGE"] = 1;
        values[valuesById[100] = "ERROR"] = 100;
        return values;
    })();

    simpleim.Heartbeat = (function() {

        /**
         * Properties of a Heartbeat.
         * @memberof simpleim
         * @interface IHeartbeat
         * @property {number|Long|null} [timestamp] Heartbeat timestamp
         * @property {string|null} [status] Heartbeat status
         */

        /**
         * Constructs a new Heartbeat.
         * @memberof simpleim
         * @classdesc Represents a Heartbeat.
         * @implements IHeartbeat
         * @constructor
         * @param {simpleim.IHeartbeat=} [properties] Properties to set
         */
        function Heartbeat(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * Heartbeat timestamp.
         * @member {number|Long} timestamp
         * @memberof simpleim.Heartbeat
         * @instance
         */
        Heartbeat.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * Heartbeat status.
         * @member {string} status
         * @memberof simpleim.Heartbeat
         * @instance
         */
        Heartbeat.prototype.status = "";

        /**
         * Creates a new Heartbeat instance using the specified properties.
         * @function create
         * @memberof simpleim.Heartbeat
         * @static
         * @param {simpleim.IHeartbeat=} [properties] Properties to set
         * @returns {simpleim.Heartbeat} Heartbeat instance
         */
        Heartbeat.create = function create(properties) {
            return new Heartbeat(properties);
        };

        /**
         * Encodes the specified Heartbeat message. Does not implicitly {@link simpleim.Heartbeat.verify|verify} messages.
         * @function encode
         * @memberof simpleim.Heartbeat
         * @static
         * @param {simpleim.IHeartbeat} message Heartbeat message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        Heartbeat.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 1, wireType 0 =*/8).int64(message.timestamp);
            if (message.status != null && Object.hasOwnProperty.call(message, "status"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.status);
            return writer;
        };

        /**
         * Encodes the specified Heartbeat message, length delimited. Does not implicitly {@link simpleim.Heartbeat.verify|verify} messages.
         * @function encodeDelimited
         * @memberof simpleim.Heartbeat
         * @static
         * @param {simpleim.IHeartbeat} message Heartbeat message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        Heartbeat.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a Heartbeat message from the specified reader or buffer.
         * @function decode
         * @memberof simpleim.Heartbeat
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {simpleim.Heartbeat} Heartbeat
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        Heartbeat.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.simpleim.Heartbeat();
            while (reader.pos < end) {
                let tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.timestamp = reader.int64();
                        break;
                    }
                case 2: {
                        message.status = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a Heartbeat message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof simpleim.Heartbeat
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {simpleim.Heartbeat} Heartbeat
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        Heartbeat.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a Heartbeat message.
         * @function verify
         * @memberof simpleim.Heartbeat
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        Heartbeat.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            if (message.status != null && message.hasOwnProperty("status"))
                if (!$util.isString(message.status))
                    return "status: string expected";
            return null;
        };

        /**
         * Creates a Heartbeat message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof simpleim.Heartbeat
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {simpleim.Heartbeat} Heartbeat
         */
        Heartbeat.fromObject = function fromObject(object) {
            if (object instanceof $root.simpleim.Heartbeat)
                return object;
            let message = new $root.simpleim.Heartbeat();
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            if (object.status != null)
                message.status = String(object.status);
            return message;
        };

        /**
         * Creates a plain object from a Heartbeat message. Also converts values to other types if specified.
         * @function toObject
         * @memberof simpleim.Heartbeat
         * @static
         * @param {simpleim.Heartbeat} message Heartbeat
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        Heartbeat.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                if ($util.Long) {
                    let long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
                object.status = "";
            }
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            if (message.status != null && message.hasOwnProperty("status"))
                object.status = message.status;
            return object;
        };

        /**
         * Converts this Heartbeat to JSON.
         * @function toJSON
         * @memberof simpleim.Heartbeat
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        Heartbeat.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for Heartbeat
         * @function getTypeUrl
         * @memberof simpleim.Heartbeat
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        Heartbeat.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/simpleim.Heartbeat";
        };

        return Heartbeat;
    })();

    simpleim.ErrorMessage = (function() {

        /**
         * Properties of an ErrorMessage.
         * @memberof simpleim
         * @interface IErrorMessage
         * @property {string|null} [code] ErrorMessage code
         * @property {string|null} [message] ErrorMessage message
         */

        /**
         * Constructs a new ErrorMessage.
         * @memberof simpleim
         * @classdesc Represents an ErrorMessage.
         * @implements IErrorMessage
         * @constructor
         * @param {simpleim.IErrorMessage=} [properties] Properties to set
         */
        function ErrorMessage(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * ErrorMessage code.
         * @member {string} code
         * @memberof simpleim.ErrorMessage
         * @instance
         */
        ErrorMessage.prototype.code = "";

        /**
         * ErrorMessage message.
         * @member {string} message
         * @memberof simpleim.ErrorMessage
         * @instance
         */
        ErrorMessage.prototype.message = "";

        /**
         * Creates a new ErrorMessage instance using the specified properties.
         * @function create
         * @memberof simpleim.ErrorMessage
         * @static
         * @param {simpleim.IErrorMessage=} [properties] Properties to set
         * @returns {simpleim.ErrorMessage} ErrorMessage instance
         */
        ErrorMessage.create = function create(properties) {
            return new ErrorMessage(properties);
        };

        /**
         * Encodes the specified ErrorMessage message. Does not implicitly {@link simpleim.ErrorMessage.verify|verify} messages.
         * @function encode
         * @memberof simpleim.ErrorMessage
         * @static
         * @param {simpleim.IErrorMessage} message ErrorMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ErrorMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.code != null && Object.hasOwnProperty.call(message, "code"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.code);
            if (message.message != null && Object.hasOwnProperty.call(message, "message"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.message);
            return writer;
        };

        /**
         * Encodes the specified ErrorMessage message, length delimited. Does not implicitly {@link simpleim.ErrorMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof simpleim.ErrorMessage
         * @static
         * @param {simpleim.IErrorMessage} message ErrorMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        ErrorMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes an ErrorMessage message from the specified reader or buffer.
         * @function decode
         * @memberof simpleim.ErrorMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {simpleim.ErrorMessage} ErrorMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ErrorMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.simpleim.ErrorMessage();
            while (reader.pos < end) {
                let tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.code = reader.string();
                        break;
                    }
                case 2: {
                        message.message = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes an ErrorMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof simpleim.ErrorMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {simpleim.ErrorMessage} ErrorMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        ErrorMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies an ErrorMessage message.
         * @function verify
         * @memberof simpleim.ErrorMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        ErrorMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.code != null && message.hasOwnProperty("code"))
                if (!$util.isString(message.code))
                    return "code: string expected";
            if (message.message != null && message.hasOwnProperty("message"))
                if (!$util.isString(message.message))
                    return "message: string expected";
            return null;
        };

        /**
         * Creates an ErrorMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof simpleim.ErrorMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {simpleim.ErrorMessage} ErrorMessage
         */
        ErrorMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.simpleim.ErrorMessage)
                return object;
            let message = new $root.simpleim.ErrorMessage();
            if (object.code != null)
                message.code = String(object.code);
            if (object.message != null)
                message.message = String(object.message);
            return message;
        };

        /**
         * Creates a plain object from an ErrorMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof simpleim.ErrorMessage
         * @static
         * @param {simpleim.ErrorMessage} message ErrorMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        ErrorMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                object.code = "";
                object.message = "";
            }
            if (message.code != null && message.hasOwnProperty("code"))
                object.code = message.code;
            if (message.message != null && message.hasOwnProperty("message"))
                object.message = message.message;
            return object;
        };

        /**
         * Converts this ErrorMessage to JSON.
         * @function toJSON
         * @memberof simpleim.ErrorMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        ErrorMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for ErrorMessage
         * @function getTypeUrl
         * @memberof simpleim.ErrorMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        ErrorMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/simpleim.ErrorMessage";
        };

        return ErrorMessage;
    })();

    simpleim.IMMessage = (function() {

        /**
         * Properties of a IMMessage.
         * @memberof simpleim
         * @interface IIMMessage
         * @property {number|null} [type] IMMessage type
         * @property {string|null} [messageId] IMMessage messageId
         * @property {number|Long|null} [timestamp] IMMessage timestamp
         * @property {string|null} [token] IMMessage token
         * @property {simpleim.ITextMessage|null} [textMessage] IMMessage textMessage
         */

        /**
         * Constructs a new IMMessage.
         * @memberof simpleim
         * @classdesc Represents a IMMessage.
         * @implements IIMMessage
         * @constructor
         * @param {simpleim.IIMMessage=} [properties] Properties to set
         */
        function IMMessage(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * IMMessage type.
         * @member {number} type
         * @memberof simpleim.IMMessage
         * @instance
         */
        IMMessage.prototype.type = 0;

        /**
         * IMMessage messageId.
         * @member {string} messageId
         * @memberof simpleim.IMMessage
         * @instance
         */
        IMMessage.prototype.messageId = "";

        /**
         * IMMessage timestamp.
         * @member {number|Long} timestamp
         * @memberof simpleim.IMMessage
         * @instance
         */
        IMMessage.prototype.timestamp = $util.Long ? $util.Long.fromBits(0,0,false) : 0;

        /**
         * IMMessage token.
         * @member {string} token
         * @memberof simpleim.IMMessage
         * @instance
         */
        IMMessage.prototype.token = "";

        /**
         * IMMessage textMessage.
         * @member {simpleim.ITextMessage|null|undefined} textMessage
         * @memberof simpleim.IMMessage
         * @instance
         */
        IMMessage.prototype.textMessage = null;

        /**
         * Creates a new IMMessage instance using the specified properties.
         * @function create
         * @memberof simpleim.IMMessage
         * @static
         * @param {simpleim.IIMMessage=} [properties] Properties to set
         * @returns {simpleim.IMMessage} IMMessage instance
         */
        IMMessage.create = function create(properties) {
            return new IMMessage(properties);
        };

        /**
         * Encodes the specified IMMessage message. Does not implicitly {@link simpleim.IMMessage.verify|verify} messages.
         * @function encode
         * @memberof simpleim.IMMessage
         * @static
         * @param {simpleim.IIMMessage} message IMMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        IMMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.type != null && Object.hasOwnProperty.call(message, "type"))
                writer.uint32(/* id 1, wireType 0 =*/8).int32(message.type);
            if (message.messageId != null && Object.hasOwnProperty.call(message, "messageId"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.messageId);
            if (message.timestamp != null && Object.hasOwnProperty.call(message, "timestamp"))
                writer.uint32(/* id 3, wireType 0 =*/24).int64(message.timestamp);
            if (message.token != null && Object.hasOwnProperty.call(message, "token"))
                writer.uint32(/* id 4, wireType 2 =*/34).string(message.token);
            if (message.textMessage != null && Object.hasOwnProperty.call(message, "textMessage"))
                $root.simpleim.TextMessage.encode(message.textMessage, writer.uint32(/* id 5, wireType 2 =*/42).fork()).ldelim();
            return writer;
        };

        /**
         * Encodes the specified IMMessage message, length delimited. Does not implicitly {@link simpleim.IMMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof simpleim.IMMessage
         * @static
         * @param {simpleim.IIMMessage} message IMMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        IMMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a IMMessage message from the specified reader or buffer.
         * @function decode
         * @memberof simpleim.IMMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {simpleim.IMMessage} IMMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        IMMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.simpleim.IMMessage();
            while (reader.pos < end) {
                let tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.type = reader.int32();
                        break;
                    }
                case 2: {
                        message.messageId = reader.string();
                        break;
                    }
                case 3: {
                        message.timestamp = reader.int64();
                        break;
                    }
                case 4: {
                        message.token = reader.string();
                        break;
                    }
                case 5: {
                        message.textMessage = $root.simpleim.TextMessage.decode(reader, reader.uint32());
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a IMMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof simpleim.IMMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {simpleim.IMMessage} IMMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        IMMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a IMMessage message.
         * @function verify
         * @memberof simpleim.IMMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        IMMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.type != null && message.hasOwnProperty("type"))
                if (!$util.isInteger(message.type))
                    return "type: integer expected";
            if (message.messageId != null && message.hasOwnProperty("messageId"))
                if (!$util.isString(message.messageId))
                    return "messageId: string expected";
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (!$util.isInteger(message.timestamp) && !(message.timestamp && $util.isInteger(message.timestamp.low) && $util.isInteger(message.timestamp.high)))
                    return "timestamp: integer|Long expected";
            if (message.token != null && message.hasOwnProperty("token"))
                if (!$util.isString(message.token))
                    return "token: string expected";
            if (message.textMessage != null && message.hasOwnProperty("textMessage")) {
                let error = $root.simpleim.TextMessage.verify(message.textMessage);
                if (error)
                    return "textMessage." + error;
            }
            return null;
        };

        /**
         * Creates a IMMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof simpleim.IMMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {simpleim.IMMessage} IMMessage
         */
        IMMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.simpleim.IMMessage)
                return object;
            let message = new $root.simpleim.IMMessage();
            if (object.type != null)
                message.type = object.type | 0;
            if (object.messageId != null)
                message.messageId = String(object.messageId);
            if (object.timestamp != null)
                if ($util.Long)
                    (message.timestamp = $util.Long.fromValue(object.timestamp)).unsigned = false;
                else if (typeof object.timestamp === "string")
                    message.timestamp = parseInt(object.timestamp, 10);
                else if (typeof object.timestamp === "number")
                    message.timestamp = object.timestamp;
                else if (typeof object.timestamp === "object")
                    message.timestamp = new $util.LongBits(object.timestamp.low >>> 0, object.timestamp.high >>> 0).toNumber();
            if (object.token != null)
                message.token = String(object.token);
            if (object.textMessage != null) {
                if (typeof object.textMessage !== "object")
                    throw TypeError(".simpleim.IMMessage.textMessage: object expected");
                message.textMessage = $root.simpleim.TextMessage.fromObject(object.textMessage);
            }
            return message;
        };

        /**
         * Creates a plain object from a IMMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof simpleim.IMMessage
         * @static
         * @param {simpleim.IMMessage} message IMMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        IMMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                object.type = 0;
                object.messageId = "";
                if ($util.Long) {
                    let long = new $util.Long(0, 0, false);
                    object.timestamp = options.longs === String ? long.toString() : options.longs === Number ? long.toNumber() : long;
                } else
                    object.timestamp = options.longs === String ? "0" : 0;
                object.token = "";
                object.textMessage = null;
            }
            if (message.type != null && message.hasOwnProperty("type"))
                object.type = message.type;
            if (message.messageId != null && message.hasOwnProperty("messageId"))
                object.messageId = message.messageId;
            if (message.timestamp != null && message.hasOwnProperty("timestamp"))
                if (typeof message.timestamp === "number")
                    object.timestamp = options.longs === String ? String(message.timestamp) : message.timestamp;
                else
                    object.timestamp = options.longs === String ? $util.Long.prototype.toString.call(message.timestamp) : options.longs === Number ? new $util.LongBits(message.timestamp.low >>> 0, message.timestamp.high >>> 0).toNumber() : message.timestamp;
            if (message.token != null && message.hasOwnProperty("token"))
                object.token = message.token;
            if (message.textMessage != null && message.hasOwnProperty("textMessage"))
                object.textMessage = $root.simpleim.TextMessage.toObject(message.textMessage, options);
            return object;
        };

        /**
         * Converts this IMMessage to JSON.
         * @function toJSON
         * @memberof simpleim.IMMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        IMMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for IMMessage
         * @function getTypeUrl
         * @memberof simpleim.IMMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        IMMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/simpleim.IMMessage";
        };

        return IMMessage;
    })();

    simpleim.TextMessage = (function() {

        /**
         * Properties of a TextMessage.
         * @memberof simpleim
         * @interface ITextMessage
         * @property {string|null} [receiverId] TextMessage receiverId
         * @property {string|null} [content] TextMessage content
         */

        /**
         * Constructs a new TextMessage.
         * @memberof simpleim
         * @classdesc Represents a TextMessage.
         * @implements ITextMessage
         * @constructor
         * @param {simpleim.ITextMessage=} [properties] Properties to set
         */
        function TextMessage(properties) {
            if (properties)
                for (let keys = Object.keys(properties), i = 0; i < keys.length; ++i)
                    if (properties[keys[i]] != null)
                        this[keys[i]] = properties[keys[i]];
        }

        /**
         * TextMessage receiverId.
         * @member {string} receiverId
         * @memberof simpleim.TextMessage
         * @instance
         */
        TextMessage.prototype.receiverId = "";

        /**
         * TextMessage content.
         * @member {string} content
         * @memberof simpleim.TextMessage
         * @instance
         */
        TextMessage.prototype.content = "";

        /**
         * Creates a new TextMessage instance using the specified properties.
         * @function create
         * @memberof simpleim.TextMessage
         * @static
         * @param {simpleim.ITextMessage=} [properties] Properties to set
         * @returns {simpleim.TextMessage} TextMessage instance
         */
        TextMessage.create = function create(properties) {
            return new TextMessage(properties);
        };

        /**
         * Encodes the specified TextMessage message. Does not implicitly {@link simpleim.TextMessage.verify|verify} messages.
         * @function encode
         * @memberof simpleim.TextMessage
         * @static
         * @param {simpleim.ITextMessage} message TextMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        TextMessage.encode = function encode(message, writer) {
            if (!writer)
                writer = $Writer.create();
            if (message.receiverId != null && Object.hasOwnProperty.call(message, "receiverId"))
                writer.uint32(/* id 1, wireType 2 =*/10).string(message.receiverId);
            if (message.content != null && Object.hasOwnProperty.call(message, "content"))
                writer.uint32(/* id 2, wireType 2 =*/18).string(message.content);
            return writer;
        };

        /**
         * Encodes the specified TextMessage message, length delimited. Does not implicitly {@link simpleim.TextMessage.verify|verify} messages.
         * @function encodeDelimited
         * @memberof simpleim.TextMessage
         * @static
         * @param {simpleim.ITextMessage} message TextMessage message or plain object to encode
         * @param {$protobuf.Writer} [writer] Writer to encode to
         * @returns {$protobuf.Writer} Writer
         */
        TextMessage.encodeDelimited = function encodeDelimited(message, writer) {
            return this.encode(message, writer).ldelim();
        };

        /**
         * Decodes a TextMessage message from the specified reader or buffer.
         * @function decode
         * @memberof simpleim.TextMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @param {number} [length] Message length if known beforehand
         * @returns {simpleim.TextMessage} TextMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        TextMessage.decode = function decode(reader, length, error) {
            if (!(reader instanceof $Reader))
                reader = $Reader.create(reader);
            let end = length === undefined ? reader.len : reader.pos + length, message = new $root.simpleim.TextMessage();
            while (reader.pos < end) {
                let tag = reader.uint32();
                if (tag === error)
                    break;
                switch (tag >>> 3) {
                case 1: {
                        message.receiverId = reader.string();
                        break;
                    }
                case 2: {
                        message.content = reader.string();
                        break;
                    }
                default:
                    reader.skipType(tag & 7);
                    break;
                }
            }
            return message;
        };

        /**
         * Decodes a TextMessage message from the specified reader or buffer, length delimited.
         * @function decodeDelimited
         * @memberof simpleim.TextMessage
         * @static
         * @param {$protobuf.Reader|Uint8Array} reader Reader or buffer to decode from
         * @returns {simpleim.TextMessage} TextMessage
         * @throws {Error} If the payload is not a reader or valid buffer
         * @throws {$protobuf.util.ProtocolError} If required fields are missing
         */
        TextMessage.decodeDelimited = function decodeDelimited(reader) {
            if (!(reader instanceof $Reader))
                reader = new $Reader(reader);
            return this.decode(reader, reader.uint32());
        };

        /**
         * Verifies a TextMessage message.
         * @function verify
         * @memberof simpleim.TextMessage
         * @static
         * @param {Object.<string,*>} message Plain object to verify
         * @returns {string|null} `null` if valid, otherwise the reason why it is not
         */
        TextMessage.verify = function verify(message) {
            if (typeof message !== "object" || message === null)
                return "object expected";
            if (message.receiverId != null && message.hasOwnProperty("receiverId"))
                if (!$util.isString(message.receiverId))
                    return "receiverId: string expected";
            if (message.content != null && message.hasOwnProperty("content"))
                if (!$util.isString(message.content))
                    return "content: string expected";
            return null;
        };

        /**
         * Creates a TextMessage message from a plain object. Also converts values to their respective internal types.
         * @function fromObject
         * @memberof simpleim.TextMessage
         * @static
         * @param {Object.<string,*>} object Plain object
         * @returns {simpleim.TextMessage} TextMessage
         */
        TextMessage.fromObject = function fromObject(object) {
            if (object instanceof $root.simpleim.TextMessage)
                return object;
            let message = new $root.simpleim.TextMessage();
            if (object.receiverId != null)
                message.receiverId = String(object.receiverId);
            if (object.content != null)
                message.content = String(object.content);
            return message;
        };

        /**
         * Creates a plain object from a TextMessage message. Also converts values to other types if specified.
         * @function toObject
         * @memberof simpleim.TextMessage
         * @static
         * @param {simpleim.TextMessage} message TextMessage
         * @param {$protobuf.IConversionOptions} [options] Conversion options
         * @returns {Object.<string,*>} Plain object
         */
        TextMessage.toObject = function toObject(message, options) {
            if (!options)
                options = {};
            let object = {};
            if (options.defaults) {
                object.receiverId = "";
                object.content = "";
            }
            if (message.receiverId != null && message.hasOwnProperty("receiverId"))
                object.receiverId = message.receiverId;
            if (message.content != null && message.hasOwnProperty("content"))
                object.content = message.content;
            return object;
        };

        /**
         * Converts this TextMessage to JSON.
         * @function toJSON
         * @memberof simpleim.TextMessage
         * @instance
         * @returns {Object.<string,*>} JSON object
         */
        TextMessage.prototype.toJSON = function toJSON() {
            return this.constructor.toObject(this, $protobuf.util.toJSONOptions);
        };

        /**
         * Gets the default type url for TextMessage
         * @function getTypeUrl
         * @memberof simpleim.TextMessage
         * @static
         * @param {string} [typeUrlPrefix] your custom typeUrlPrefix(default "type.googleapis.com")
         * @returns {string} The default type url
         */
        TextMessage.getTypeUrl = function getTypeUrl(typeUrlPrefix) {
            if (typeUrlPrefix === undefined) {
                typeUrlPrefix = "type.googleapis.com";
            }
            return typeUrlPrefix + "/simpleim.TextMessage";
        };

        return TextMessage;
    })();

    return simpleim;
})();

export { $root as default };

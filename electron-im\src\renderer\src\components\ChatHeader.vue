<!-- 聊天头部组件 -->
<template>
  <div class="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
    <UserDetailPopover :user-id="currentContact?.user?.id || null" v-model:open="showPopover">
      <div
        class="flex items-center gap-3 cursor-pointer hover:bg-gray-50 rounded-lg p-2 -m-2 transition-colors"
      >
        <UserAvatar :name="currentContact?.name || '用户'" size="medium" />
        <div>
          <h3 class="font-medium text-base text-gray-900 m-0">
            {{ currentContact?.name || '选择聊天' }}
          </h3>
          <p class="text-xs text-gray-500 m-0">{{ currentContact?.status || '离线' }}</p>
        </div>
      </div>
    </UserDetailPopover>
    <div class="flex items-center gap-2">
      <button
        class="bg-transparent border-none cursor-pointer p-2 rounded text-gray-500 text-sm hover:bg-gray-100"
        @click="$emit('more-options')"
      >
        ⋯
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import UserAvatar from './UserAvatar.vue'
import UserDetailPopover from './UserDetailPopover.vue'
import type { User } from '../api'

interface Contact {
  id: string
  name: string
  avatar: string
  status: string
  lastMessage: string
  lastMessageTime: Date
  user: User
  hasLastMessage?: boolean
}

interface Props {
  currentContact: Contact | null
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'more-options': []
}>()

// 控制 Popover 显示状态
const showPopover = ref(false)
</script>
